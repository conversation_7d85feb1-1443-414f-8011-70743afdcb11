import { useState, useRef, useEffect, useMemo, useCallback, memo } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { Card } from '@/components/ui/card';
import { TypographyConfig } from '@/components/ui/typography-config';
import { LaTeXRenderer, hasLaTeX } from '@/components/ui/latex-renderer';
import { EnhancedToolbar } from '@/components/ui/enhanced-toolbar';
import { FileText } from 'lucide-react';
import html2pdf from 'html2pdf.js';
import html2canvas from 'html2canvas';
import { motion } from 'motion/react';

// Memoized page component to prevent unnecessary re-renders
const MemoizedPage = memo(({
  pageText,
  index,
  renderPaperBackground,
  totalPages,
  showPageNumbers
}: {
  pageText: string;
  index: number;
  renderPaperBackground: (pageText: string) => JSX.Element;
  totalPages: number;
  showPageNumbers: boolean;
}) => (
  <div
    key={index}
    data-page-index={index}
    className="w-full shadow-lg rounded-lg overflow-hidden bg-white relative"
    style={{
      aspectRatio: '210/297', // A4 ratio
      minHeight: '600px', // Increased for more realistic page size
      maxWidth: '100%',
      width: '100%'
    }}
  >
    {renderPaperBackground(pageText)}
    {totalPages > 1 && showPageNumbers && (
      <div className="absolute bottom-4 right-4 text-xs text-neutral-400 bg-white/80 px-2 py-1 rounded z-10">
        Page {index + 1}
      </div>
    )}
  </div>
));

const HandwritingEditor = () => {
  // Initialize with defaults first, then load from localStorage asynchronously
  const [text, setText] = useState('');
  const [selectedFont, setSelectedFont] = useState('handwriting');
  const [background, setBackground] = useState('lined');

  const [typographyConfig, setTypographyConfig] = useState<TypographyConfig>({
    fontSize: 18,
    lineHeight: 1.6,
    letterSpacing: 0,
    wordSpacing: 0,
    textColor: '#000000',
    marginTop: 10,
    marginBottom: 10,
    marginLeft: 10,
    marginRight: 10
  });

  const [showPageNumbers, setShowPageNumbers] = useState(false);
  const [customBackground, setCustomBackground] = useState('');

  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [textPosition, setTextPosition] = useState({ x: 0, y: 0 });
  const [isLoaded, setIsLoaded] = useState(false);

  const paperRef = useRef<HTMLDivElement>(null);

  // Load from localStorage asynchronously after mount to prevent blocking initial render
  useEffect(() => {
    const loadFromStorage = async () => {
      try {
        // Use requestIdleCallback if available, otherwise setTimeout
        const scheduleWork = (callback: () => void) => {
          if ('requestIdleCallback' in window) {
            requestIdleCallback(callback);
          } else {
            setTimeout(callback, 0);
          }
        };

        scheduleWork(() => {
          const savedText = localStorage.getItem('handwriting-editor-text');
          const savedFont = localStorage.getItem('handwriting-editor-font');
          const savedBackground = localStorage.getItem('handwriting-editor-background');
          const savedTypography = localStorage.getItem('handwriting-editor-typography');
          const savedPageNumbers = localStorage.getItem('handwriting-editor-show-page-numbers');
          const savedCustomBg = localStorage.getItem('handwriting-editor-custom-background');
          const savedTextPosition = localStorage.getItem('handwriting-editor-text-position');

          if (savedText) setText(savedText);
          if (savedFont) setSelectedFont(savedFont);
          if (savedBackground) setBackground(savedBackground);
          if (savedTypography) {
            try {
              setTypographyConfig(JSON.parse(savedTypography));
            } catch (e) {
              console.warn('Failed to parse typography config:', e);
            }
          }
          if (savedPageNumbers) setShowPageNumbers(savedPageNumbers === 'true');
          if (savedCustomBg) setCustomBackground(savedCustomBg);
          if (savedTextPosition) {
            try {
              setTextPosition(JSON.parse(savedTextPosition));
            } catch (e) {
              console.warn('Failed to parse text position:', e);
            }
          }

          setIsLoaded(true);
        });
      } catch (error) {
        console.warn('Failed to load from localStorage:', error);
        setIsLoaded(true);
      }
    };

    loadFromStorage();
  }, []);

  // Debounced localStorage save - only save after initial load to prevent saving defaults
  useEffect(() => {
    if (!isLoaded) return; // Don't save until we've loaded from localStorage

    const timeoutId = setTimeout(() => {
      // Use requestIdleCallback for non-blocking localStorage operations
      const saveToStorage = () => {
        try {
          const batch = {
            'handwriting-editor-text': text,
            'handwriting-editor-font': selectedFont,
            'handwriting-editor-background': background,
            'handwriting-editor-typography': JSON.stringify(typographyConfig),
            'handwriting-editor-text-position': JSON.stringify(textPosition),
            'handwriting-editor-show-page-numbers': showPageNumbers.toString(),
            'handwriting-editor-custom-background': customBackground
          };

          Object.entries(batch).forEach(([key, value]) => {
            localStorage.setItem(key, value);
          });
        } catch (error) {
          console.warn('Failed to save to localStorage:', error);
        }
      };

      if ('requestIdleCallback' in window) {
        requestIdleCallback(saveToStorage);
      } else {
        setTimeout(saveToStorage, 0);
      }
    }, 500); // Increased debounce to 500ms for better performance

    return () => clearTimeout(timeoutId);
  }, [text, selectedFont, background, typographyConfig, textPosition, showPageNumbers, customBackground, isLoaded]);

  const fontOptions = [
    { value: 'handwriting', label: 'Kalam (Default)', class: 'font-handwriting' },
    { value: 'handwriting-alt', label: 'Caveat', class: 'font-handwriting-alt' },
    { value: 'handwriting-dance', label: 'Dancing Script', class: 'font-handwriting-dance' },
    { value: 'handwriting-architect', label: 'Architects Daughter', class: 'font-handwriting-architect' },
    { value: 'handwriting-indie', label: 'Indie Flower', class: 'font-handwriting-indie' },
  ];

  const selectedFontClass = useMemo(() =>
    fontOptions.find(f => f.value === selectedFont)?.class || 'font-handwriting',
    [selectedFont]
  );

  // Memoize LaTeX detection to avoid repeated regex checks
  const textHasLaTeX = useMemo(() => hasLaTeX(text), [text]);

  // Debounced text handler to reduce re-renders while typing
  const handleTextChange = useCallback((e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setText(e.target.value);
  }, []);

  // Memoize expensive calculations
  const memoizedSplitText = useMemo(() => {
    if (!text.trim()) return [''];

    // More realistic page calculations based on A4 dimensions
    // A4 is 210mm x 297mm, with typical margins of 25mm
    // Usable area: 160mm x 247mm
    const baseCharsPerLine = 65; // Realistic for handwriting
    const baseLinesPerPage = 30; // Realistic for A4 with margins

    // Adjust based on font size
    const fontSizeRatio = typographyConfig.fontSize / 18; // 18px is our base
    const lineHeightRatio = typographyConfig.lineHeight / 1.6; // 1.6 is our base

    const charsPerLine = Math.floor(baseCharsPerLine / fontSizeRatio);
    const linesPerPage = Math.floor(baseLinesPerPage / (fontSizeRatio * lineHeightRatio));

    const lines = text.split('\n');
    const pages = [];
    let currentPage = '';
    let currentLineCount = 0;

    for (const line of lines) {
      // Calculate how many visual lines this text line will take
      const visualLines = Math.max(1, Math.ceil(line.length / charsPerLine));

      // Check if adding this line would exceed page capacity
      if (currentLineCount + visualLines > linesPerPage && currentPage.trim()) {
        pages.push(currentPage.trim());
        currentPage = line + '\n';
        currentLineCount = visualLines;
      } else {
        currentPage += line + '\n';
        currentLineCount += visualLines;
      }
    }

    // Add the last page if it has content
    if (currentPage.trim()) {
      pages.push(currentPage.trim());
    }

    return pages.length > 0 ? pages : [''];
  }, [text, typographyConfig.fontSize, typographyConfig.lineHeight]);

  const handleCustomBackgroundUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setCustomBackground(result);
        setBackground('custom');
      };
      reader.readAsDataURL(file);
    }
  };

  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    setDragOffset({
      x: e.clientX - textPosition.x,
      y: e.clientY - textPosition.y
    });
  }, [textPosition.x, textPosition.y]);

  // Throttled mouse move to prevent excessive updates
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (isDragging) {
      // Use requestAnimationFrame for smooth updates and prevent excessive calls
      if (!window.requestAnimationFrame) return;

      requestAnimationFrame(() => {
        setTextPosition({
          x: e.clientX - dragOffset.x,
          y: e.clientY - dragOffset.y
        });
      });
    }
  }, [isDragging, dragOffset.x, dragOffset.y]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  const resetTextPosition = useCallback(() => {
    setTextPosition({ x: 0, y: 0 });
  }, []);

  const generatePDF = async () => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating PDF');
      return;
    }

    const element = paperRef.current;
    const opt = {
      margin: 0,
      filename: 'handwritten-note.pdf',
      image: { type: 'jpeg', quality: 0.98 },
      html2canvas: {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      },
      jsPDF: { unit: 'mm', format: 'a4', orientation: 'portrait' }
    };

    try {
      await html2pdf().set(opt).from(element).save();
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  const generateImage = async () => {
    if (!paperRef.current || !text.trim()) {
      alert('Please write some text before generating image');
      return;
    }

    try {
      const pageElements = paperRef.current.querySelectorAll('[data-page-index]');

      if (pageElements.length === 0) {
        // Fallback to single image if no page elements found
        const canvas = await html2canvas(paperRef.current, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false
        });

        const link = document.createElement('a');
        link.download = 'handwritten-note.png';
        link.href = canvas.toDataURL('image/png');
        link.click();
        return;
      }

      // Generate multiple images, one per page
      for (let i = 0; i < pageElements.length; i++) {
        const pageElement = pageElements[i] as HTMLElement;

        const canvas = await html2canvas(pageElement, {
          scale: 2,
          useCORS: true,
          backgroundColor: '#ffffff',
          logging: false
        });

        const link = document.createElement('a');
        link.download = `handwritten-note-page-${i + 1}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();

        // Small delay between downloads to avoid browser blocking
        if (i < pageElements.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }
    } catch (error) {
      console.error('Error generating images:', error);
      alert('Error generating images. Please try again.');
    }
  };



  // Memoize text style to prevent recalculation
  const textStyle = useMemo(() => ({
    fontSize: `${typographyConfig.fontSize}px`,
    lineHeight: background === 'lined' ? '25px' : typographyConfig.lineHeight,
    letterSpacing: `${typographyConfig.letterSpacing}px`,
    wordSpacing: `${typographyConfig.wordSpacing}px`,
    color: background === 'blueprint' ? '#ffffff' : typographyConfig.textColor,
    transform: `translate(${textPosition.x}px, ${textPosition.y}px)`,
    cursor: isDragging ? 'grabbing' : 'grab'
  }), [
    typographyConfig.fontSize,
    typographyConfig.lineHeight,
    typographyConfig.letterSpacing,
    typographyConfig.wordSpacing,
    typographyConfig.textColor,
    background,
    textPosition.x,
    textPosition.y,
    isDragging
  ]);

  const renderTextContent = useCallback((pageText: string) => {
    const content = hasLaTeX(pageText) ? (
      <LaTeXRenderer
        text={pageText || 'Your handwritten text will appear here...'}
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      />
    ) : (
      <div
        className={`${selectedFontClass} whitespace-pre-wrap`}
        style={textStyle}
      >
        {pageText || 'Your handwritten text will appear here...'}
      </div>
    );

    return (
      <div
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        className="relative"
      >
        {content}
      </div>
    );
  }, [
    textStyle,
    selectedFontClass,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp
  ]);



  const renderPaperBackground = useCallback((pageText: string) => {
    const textContent = renderTextContent(pageText);
    const marginStyle = {
      paddingTop: `${typographyConfig.marginTop}px`,
      paddingBottom: `${typographyConfig.marginBottom}px`,
      paddingLeft: `${typographyConfig.marginLeft}px`,
      paddingRight: `${typographyConfig.marginRight}px`
    };

    switch (background) {
      case 'plain':
        return (
          <div className="w-full h-full relative bg-white" style={marginStyle}>
            {textContent}
          </div>
        );

      case 'warm-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #fefcf3 0%, #fdf8e8 50%, #fcf5e0 100%)',
              boxShadow: 'inset 0 0 100px rgba(255, 193, 7, 0.1)'
            }}
          >
            {textContent}
          </div>
        );

      case 'cool-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
              boxShadow: 'inset 0 0 100px rgba(59, 130, 246, 0.08)'
            }}
          >
            {textContent}
          </div>
        );

      case 'fluorescent':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: 'linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 50%, #d1fae5 100%)',
              boxShadow: 'inset 0 0 80px rgba(34, 197, 94, 0.06)'
            }}
          >
            {textContent}
          </div>
        );

      case 'aged-paper':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `
                radial-gradient(circle at 20% 80%, rgba(139, 69, 19, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(139, 69, 19, 0.02) 0%, transparent 50%),
                linear-gradient(135deg, #faf7f0 0%, #f5f0e8 50%, #f0ebe3 100%)
              `,
              boxShadow: 'inset 0 0 120px rgba(139, 69, 19, 0.08)'
            }}
          >
            {textContent}
          </div>
        );

      case 'textured-white':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `
                radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.01) 0%, transparent 50%),
                linear-gradient(45deg, #ffffff 25%, #fefefe 25%, #fefefe 50%, #ffffff 50%, #ffffff 75%, #fefefe 75%, #fefefe)
              `,
              backgroundSize: '4px 4px, 4px 4px, 8px 8px',
              backgroundColor: '#ffffff'
            }}
          >
            {textContent}
          </div>
        );

      case 'custom':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              backgroundImage: customBackground ? `url(${customBackground})` : 'none',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat',
              backgroundColor: customBackground ? 'transparent' : '#ffffff'
            }}
          >
            {/* Very light overlay for better text readability without graying out */}
            {customBackground && (
              <div className="absolute inset-0 bg-white/5"></div>
            )}

            {/* Text content */}
            <div className="relative z-10">
              {textContent}
            </div>
          </div>
        );

      case 'dots':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `radial-gradient(circle, #d1d5db 1px, transparent 1px)`,
              backgroundSize: '20px 20px',
              backgroundPosition: '10px 10px'
            }}
          >
            {textContent}
          </div>
        );

      case 'graph':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                linear-gradient(to right, #e5e7eb 1px, transparent 1px),
                linear-gradient(to bottom, #e5e7eb 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      case 'music':
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                repeating-linear-gradient(
                  0deg,
                  transparent,
                  transparent 30px,
                  #000000 30px,
                  #000000 31px,
                  transparent 31px,
                  transparent 40px,
                  #000000 40px,
                  #000000 41px,
                  transparent 41px,
                  transparent 50px,
                  #000000 50px,
                  #000000 51px,
                  transparent 51px,
                  transparent 60px,
                  #000000 60px,
                  #000000 61px,
                  transparent 61px,
                  transparent 70px,
                  #000000 70px,
                  #000000 71px,
                  transparent 71px,
                  transparent 120px
                )
              `
            }}
          >
            {textContent}
          </div>
        );

      case 'parchment':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              background: `linear-gradient(45deg, #f4f1e8 25%, #f7f4ec 25%, #f7f4ec 50%, #f4f1e8 50%, #f4f1e8 75%, #f7f4ec 75%, #f7f4ec)`,
              backgroundColor: '#f9f6f0',
              backgroundSize: '20px 20px',
              boxShadow: 'inset 0 0 50px rgba(139, 69, 19, 0.1)'
            }}
          >
            <div style={{ color: '#8b4513' }}>
              {textContent}
            </div>
          </div>
        );

      case 'blueprint':
        return (
          <div
            className="w-full h-full relative"
            style={{
              ...marginStyle,
              backgroundColor: '#1e3a8a',
              backgroundImage: `
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px)
              `,
              backgroundSize: '20px 20px'
            }}
          >
            {textContent}
          </div>
        );

      default: // lined
        return (
          <div
            className="w-full h-full relative bg-white"
            style={{
              ...marginStyle,
              backgroundImage: `
                linear-gradient(to bottom,
                  transparent 0px,
                  transparent 24px,
                  #e5e7eb 24px,
                  #e5e7eb 25px,
                  transparent 25px
                )`,
              backgroundSize: '100% 25px',
              backgroundRepeat: 'repeat-y'
            }}
          >
            {/* Red margin line */}
            <div
              className="absolute top-0 bottom-0 w-px bg-red-300"
              style={{ left: `${typographyConfig.marginLeft + 32}px` }}
            ></div>

            {/* Text content */}
            {textContent}
          </div>
        );
    }
  }, [
    renderTextContent,
    typographyConfig.marginTop,
    typographyConfig.marginBottom,
    typographyConfig.marginLeft,
    typographyConfig.marginRight,
    background,
    customBackground
  ]);

  return (
    <div className="min-h-screen bg-white dark:bg-black relative overflow-hidden">
      {/* Background Grid Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:24px_24px] dark:bg-[linear-gradient(to_right,#ffffff0f_1px,transparent_1px),linear-gradient(to_bottom,#ffffff0f_1px,transparent_1px)]"></div>
      
      {/* Radial Gradient Overlay */}
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)] dark:bg-[radial-gradient(circle_at_50%_50%,rgba(120,119,198,0.1),transparent_50%)]"></div>
      
      <div className="relative z-10 p-4">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8"
          >
            <h1 className="text-4xl font-bold text-neutral-800 dark:text-neutral-200 mb-2 flex items-center justify-center gap-3">
              <FileText className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />
              Handwritten Text to PDF
            </h1>
            <p className="text-neutral-600 dark:text-neutral-400">Transform your text into beautiful handwritten-style PDFs</p>
          </motion.div>

          {/* Enhanced Toolbar */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="mb-6"
          >
            <EnhancedToolbar
              selectedFont={selectedFont}
              onFontChange={setSelectedFont}
              fontOptions={fontOptions}
              selectedBackground={background}
              onBackgroundChange={setBackground}
              customBackground={customBackground}
              onCustomBackgroundUpload={handleCustomBackgroundUpload}
              typographyConfig={typographyConfig}
              onTypographyChange={setTypographyConfig}
              showPageNumbers={showPageNumbers}
              onShowPageNumbersChange={setShowPageNumbers}
              onResetTextPosition={resetTextPosition}
              onGeneratePDF={generatePDF}
              onGenerateImage={generateImage}
              hasText={!!text.trim()}
            />
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Input Area */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                <h2 className="text-xl font-semibold mb-4 text-neutral-800 dark:text-neutral-200">Write Your Text</h2>
                <Textarea
                  value={text}
                  onChange={handleTextChange}
                  placeholder="Start writing your text here... It will appear in handwritten style on the right!

LaTeX Support:
• Inline math: $x^2 + y^2 = z^2$
• Display math: $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
• Fractions: $\frac{a}{b}$, Greek letters: $\alpha, \beta, \gamma$"
                  className="min-h-[500px] resize-none text-base leading-relaxed bg-white/50 dark:bg-black/20 border-neutral-200/20 dark:border-neutral-700/20 focus:border-indigo-500/50 dark:focus:border-indigo-400/50"
                />
                <div className="mt-4 text-sm text-neutral-500 dark:text-neutral-400 space-y-1">
                  <div>Characters: {text.length}</div>
                  {textHasLaTeX && (
                    <div className="text-green-600 dark:text-green-400">
                      ✓ LaTeX detected and will be rendered
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>

            {/* Preview Area */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Card className="p-6 bg-white/80 dark:bg-black/40 backdrop-blur-md border border-neutral-200/20 dark:border-neutral-800/20 shadow-[0_0_24px_rgba(34,_42,_53,_0.06),_0_1px_1px_rgba(0,_0,_0,_0.05),_0_0_0_1px_rgba(34,_42,_53,_0.04),_0_0_4px_rgba(34,_42,_53,_0.08),_0_16px_68px_rgba(47,_48,_55,_0.05),_0_1px_0_rgba(255,_255,_255,_0.1)_inset] dark:shadow-[0_0_24px_rgba(0,_0,_0,_0.3)] hover:shadow-lg transition-shadow duration-300">
                {(() => {
                  const pages = memoizedSplitText;
                  const totalPages = pages.length;

                  return (
                    <>
                      <div className="flex items-center justify-between mb-4">
                        <h2 className="text-xl font-semibold text-neutral-800 dark:text-neutral-200">
                          Preview (A4 Format)
                        </h2>
                        {totalPages > 1 && (
                          <span className="text-sm text-neutral-600 dark:text-neutral-400">
                            {totalPages} page{totalPages > 1 ? 's' : ''}
                          </span>
                        )}
                      </div>

                      <div className="space-y-6 max-h-[700px] overflow-y-auto">
                        <div ref={paperRef} className="space-y-6">
                          {pages.map((pageText, index) => (
                            <MemoizedPage
                              key={index}
                              pageText={pageText}
                              index={index}
                              renderPaperBackground={renderPaperBackground}
                              totalPages={totalPages}
                              showPageNumbers={showPageNumbers}
                            />
                          ))}
                        </div>
                      </div>
                    </>
                  );
                })()}
              </Card>
            </motion.div>
          </div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-8 text-center text-neutral-500 dark:text-neutral-400 text-sm space-y-2"
          >
            <p>Your text will be converted to PDF or PNG with handwritten-style fonts</p>
            <p>✨ Features: LaTeX math support, custom typography, ink colors, and multiple paper styles</p>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HandwritingEditor;
